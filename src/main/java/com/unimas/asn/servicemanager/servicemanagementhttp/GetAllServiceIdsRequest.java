/*************************************************************/
/* Copyright (C) 2025 OSS Nokalva, Inc.  All rights reserved.*/
/*************************************************************/

/* THIS FILE IS PROPRIETARY MATERIAL OF OSS NOKALVA, INC.
 * AND MAY BE USED ONLY BY DIRECT LICENSEES OF OSS NOKALVA, INC.
 * THIS FILE MAY NOT BE DISTRIBUTED.
 * THIS COPYRIGHT STATEMENT MAY NOT BE REMOVED. */

/* Generated for: fb (Trial), License 88957Z 88957Z. */
/* Abstract syntax: service */
/* ASN.1 Java project: com.unimas.asn.servicemanager.Servicemanager */
/* Created: Tue Jun  3 10:30:13 2025 */
/* ASN.1 Compiler for Java version: 8.7 */
/* ASN.1 compiler options and file names specified:
 * -soed -output com.unimas.asn.servicemanager -coer -root -sampleCode pdus
 * -messageFormat msvc D:/history/ASN/service.asn
 */


package com.unimas.asn.servicemanager.servicemanagementhttp;

import com.oss.asn1.*;
import com.oss.metadata.*;

/**
 * Define the GetAllServiceIdsRequest ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see Sequence
 */

public class GetAllServiceIdsRequest extends Sequence {
    
    /**
     * The default constructor.
     */
    public GetAllServiceIdsRequest()
    {
    }
    
    /**
     * Construct with AbstractData components.
     */
    public GetAllServiceIdsRequest(ContentMessageType messageType)
    {
	setMessageType(messageType);
    }
    
    public void initComponents()
    {
	mComponents[0] = ContentMessageType.setInterfaceIpService;
    }
    
    // Instance initializer
    {
	mComponents = new AbstractData[1];
    }
    
    // Method to create a specific component instance
    public AbstractData createInstance(int index)
    {
	switch (index) {
	    case 0:
		return ContentMessageType.setInterfaceIpService;
	    default:
		throw new InternalError("AbstractCollection.createInstance()");
	}
	
    }
    
    
    // Methods for field "messageType"
    public ContentMessageType getMessageType()
    {
	return (ContentMessageType)mComponents[0];
    }
    
    public void setMessageType(ContentMessageType messageType)
    {
	mComponents[0] = messageType;
    }
    
    
    /**
     * Initialize the type descriptor.
     */
    private static final SequenceInfo c_typeinfo = new SequenceInfo (
	new Tags (
	    new short[] {
		0x0010
	    }
	),
	new QName (
	    "com.unimas.asn.servicemanager.servicemanagementhttp",
	    "GetAllServiceIdsRequest"
	),
	new QName (
	    "ServiceManagementHTTP",
	    "GetAllServiceIdsRequest"
	),
	536603,
	null,
	new FieldsList (
	    new SequenceFieldInfo[] {
		new SequenceFieldInfo (
		    new TypeInfoRef (
			new EnumeratedInfo (
			    new Tags (
				new short[] {
				    (short)0x8000
				}
			    ),
			    new QName (
				"com.unimas.asn.servicemanager.servicemanagementhttp",
				"ContentMessageType"
			    ),
			    new QName (
				"ServiceManagementHTTP",
				"ContentMessageType"
			    ),
			    536607,
			    null,
			    new MemberList (
				new MemberListElement[] {
				    new MemberListElement (
					"setInterfaceIpService",
					0
				    ),
				    new MemberListElement (
					"addService",
					1
				    ),
				    new MemberListElement (
					"updateService",
					2
				    ),
				    new MemberListElement (
					"deleteService",
					3
				    ),
				    new MemberListElement (
					"controlService",
					4
				    ),
				    new MemberListElement (
					"queryServiceStatus",
					5
				    ),
				    new MemberListElement (
					"queryServiceConfig",
					6
				    ),
				    new MemberListElement (
					"reportAlarm",
					7
				    ),
				    new MemberListElement (
					"queryWorkStatus",
					8
				    ),
				    new MemberListElement (
					"getAllServiceIds",
					9
				    ),
				    new MemberListElement (
					"sendPacketStats",
					10
				    ),
				    new MemberListElement (
					"receivePacketStats",
					11
				    ),
				    new MemberListElement (
					"checkCommStatusService",
					12
				    ),
				    new MemberListElement (
					"hostMngService",
					13
				    ),
				    new MemberListElement (
					"sourceDeviceService",
					14
				    )
				}
			    ),
			    0,
			    ContentMessageType.setInterfaceIpService
			)
		    ),
		    "messageType",
		    0,
		    2,
		    null
		)
	    }
	),
	0,
	new TagDecoders (
	    new TagDecoder[] {
		new TagDecoder (
		    new TagDecoderElement[] {
			new TagDecoderElement((short)0x8000, 0)
		    }
		)
	    }
	),
	0
    );
    
    /**
     * Get the type descriptor (TypeInfo) of 'this' GetAllServiceIdsRequest object.
     */
    public TypeInfo getTypeInfo()
    {
	return c_typeinfo;
    }
    
    /**
     * Get the static type descriptor (TypeInfo) of 'this' GetAllServiceIdsRequest object.
     */
    public static TypeInfo getStaticTypeInfo()
    {
	return c_typeinfo;
    }
    
} // End class definition for GetAllServiceIdsRequest

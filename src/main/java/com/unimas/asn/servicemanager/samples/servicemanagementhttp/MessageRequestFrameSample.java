
/* THIS SAMPLE PROGRAM IS PROVIDED AS IS. THE SAMPLE PROGRAM AND ANY RESULTS
 * OBTAINED FROM IT ARE PROVIDED WITHOUT ANY WARRANTIES OR REPRESENTATIONS,
 * EXPRESS, IMPLIED OR STATUTORY. */

package com.unimas.asn.servicemanager.samples.servicemanagementhttp;

import java.util.Enumeration;
import java.io.PrintStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import com.oss.asn1.*;
import com.oss.util.*;
import com.unimas.asn.servicemanager.servicemanagementhttp.*;

/**
 * Define sample code for the MessageRequestFrame ASN.1 type included in the ServiceManagementHTTP ASN.1 module.
 * @see com.unimas.asn.servicemanager.servicemanagementhttp.MessageRequestFrame
 */

public class MessageRequestFrameSample extends SampleUtil {

    /**
     * The default constructor. The class is not instantiable.
     */
    private MessageRequestFrameSample() {}

    /**
     * Create Sample Value.
     */
    public static MessageRequestFrame createSampleValue()
    {
	MessageRequestFrame value = new MessageRequestFrame();
	value.setVersion(new Uint8(1));
	value.setContent(new MessageRequestFrame.Content());
	{
	    MessageRequestFrame.Content content_2 = value.getContent();
	    content_2.setSetInterfaceIpRequest(new SetInterfaceIpRequest());
	    {
		SetInterfaceIpRequest setInterfaceIpRequest_3 = (SetInterfaceIpRequest)content_2.getChosenValue();
		setInterfaceIpRequest_3.setMessageType(ContentMessageType.setInterfaceIpService);
		setInterfaceIpRequest_3.setInterfaceType(InterfaceType.management);
		setInterfaceIpRequest_3.setIpAddress(IPAddress.createIPAddressWithIpV4(new IPv4Address(new byte[]
		{
		    (byte)0x00, (byte)0x00, (byte)0x00, (byte)0x00
		})));
		setInterfaceIpRequest_3.setSubnetMask(IPAddress.createIPAddressWithIpV4(new IPv4Address(new byte[]
		{
		    (byte)0x00, (byte)0x00, (byte)0x00, (byte)0x00
		})));
		setInterfaceIpRequest_3.setGateway(IPAddress.createIPAddressWithIpV4(new IPv4Address(new byte[]
		{
		    (byte)0x00, (byte)0x00, (byte)0x00, (byte)0x00
		})));
		setInterfaceIpRequest_3.setIproute(IPAddress.createIPAddressWithIpV4(new IPv4Address(new byte[]
		{
		    (byte)0x00, (byte)0x00, (byte)0x00, (byte)0x00
		})));
	    }
	}
	return value;
    }
    
    public static void printValue(MessageRequestFrame value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("version ");
	    s.print(value.getVersion().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("content ");
	    printValue(value.getContent(), s);
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(MessageRequestFrame.Content value, PrintStream s)
    {
	
	switch (value.getChosenFlag()) {
	case MessageRequestFrame.Content.setInterfaceIpRequest_chosen:
	    s.print("setInterfaceIpRequest : ");
	    printValue(((SetInterfaceIpRequest)value.getChosenValue()), s);
	    break;
	case MessageRequestFrame.Content.serviceConfigRequest_chosen:
	    s.print("serviceConfigRequest : ");
	    printValue(((ServiceConfigRequest)value.getChosenValue()), s);
	    break;
	case MessageRequestFrame.Content.serviceControlRequest_chosen:
	    s.print("serviceControlRequest : ");
	    printValue(((ServiceControlRequest)value.getChosenValue()), s);
	    break;
	case MessageRequestFrame.Content.serviceStatusQueryRequest_chosen:
	    s.print("serviceStatusQueryRequest : ");
	    printValue(((ServiceStatusQueryRequest)value.getChosenValue()), s);
	    break;
	case MessageRequestFrame.Content.serviceConfigQueryRequest_chosen:
	    s.print("serviceConfigQueryRequest : ");
	    printValue(((ServiceConfigQueryRequest)value.getChosenValue()), s);
	    break;
	case MessageRequestFrame.Content.alarmReportRequest_chosen:
	    s.print("alarmReportRequest : ");
	    printValue(((AlarmReportRequest)value.getChosenValue()), s);
	    break;
	case MessageRequestFrame.Content.workStatusRequest_chosen:
	    s.print("workStatusRequest : ");
	    printValue(((WorkStatusRequest)value.getChosenValue()), s);
	    break;
	case MessageRequestFrame.Content.getAllServiceIdsRequest_chosen:
	    s.print("getAllServiceIdsRequest : ");
	    printValue(((GetAllServiceIdsRequest)value.getChosenValue()), s);
	    break;
	case MessageRequestFrame.Content.sendPacketStatsRequest_chosen:
	    s.print("sendPacketStatsRequest : ");
	    printValue(((SendPacketStatsRequest)value.getChosenValue()), s);
	    break;
	case MessageRequestFrame.Content.receivePacketStatsRequest_chosen:
	    s.print("receivePacketStatsRequest : ");
	    printValue(((ReceivePacketStatsRequest)value.getChosenValue()), s);
	    break;
	case MessageRequestFrame.Content.checkCommStatusRequest_chosen:
	    s.print("checkCommStatusRequest : ");
	    printValue(((CheckCommStatusRequest)value.getChosenValue()), s);
	    break;
	case MessageRequestFrame.Content.queryHostMngRequest_chosen:
	    s.print("queryHostMngRequest : ");
	    printValue(((QueryHostMngRequest)value.getChosenValue()), s);
	    break;
	case MessageRequestFrame.Content.setHostMngRequest_chosen:
	    s.print("setHostMngRequest : ");
	    printValue(((SetHostMngRequest)value.getChosenValue()), s);
	    break;
	case MessageRequestFrame.Content.querySourceDeviceRequest_chosen:
	    s.print("querySourceDeviceRequest : ");
	    printValue(((QuerySourceDeviceRequest)value.getChosenValue()), s);
	    break;
	case MessageRequestFrame.Content.setSourceDeviceRequest_chosen:
	    s.print("setSourceDeviceRequest : ");
	    printValue(((SetSourceDeviceRequest)value.getChosenValue()), s);
	    break;
	default:
	    s.print("<unknown choice>");
	}
    }
    public static void printValue(SetInterfaceIpRequest value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("interfaceType ");
	    s.print(value.getInterfaceType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("ipAddress ");
	    printValue(value.getIpAddress(), s);
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("subnetMask ");
	    printValue(value.getSubnetMask(), s);
	    if (value.hasGateway()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("gateway ");
		printValue(value.getGateway(), s);
	    }
	    if (value.hasIproute()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("iproute ");
		printValue(value.getIproute(), s);
	    }
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(ServiceConfigRequest value, PrintStream s)
    {
	
	switch (value.getChosenFlag()) {
	case ServiceConfigRequest.addServiceRequest_chosen:
	    s.print("addServiceRequest : ");
	    printValue(((AddServiceRequest)value.getChosenValue()), s);
	    break;
	case ServiceConfigRequest.updateServiceRequest_chosen:
	    s.print("updateServiceRequest : ");
	    printValue(((UpdateServiceRequest)value.getChosenValue()), s);
	    break;
	case ServiceConfigRequest.deleteServiceRequest_chosen:
	    s.print("deleteServiceRequest : ");
	    printValue(((DeleteServiceRequest)value.getChosenValue()), s);
	    break;
	default:
	    s.print("<unknown choice>");
	}
    }
    public static void printValue(ServiceControlRequest value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("serviceStartOrStop ");
	    s.print(value.getServiceStartOrStop().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("serviceId ");
	    s.print(value.getServiceId().longValue());
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(ServiceStatusQueryRequest value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("serviceId ");
	    s.print(value.getServiceId().longValue());
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(ServiceConfigQueryRequest value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("serviceId ");
	    s.print(value.getServiceId().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("network ");
	    s.print(value.getNetwork().longValue());
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(AlarmReportRequest value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("serviceId ");
	    s.print(value.getServiceId().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("alarmType ");
	    s.print(value.getAlarmType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("alarmCode ");
	    s.print(value.getAlarmCode().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("alarmStatus ");
	    s.print(value.getAlarmStatus().longValue());
	    if (value.hasHappenTime()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("happenTime ");
		s.print(value.getHappenTime());
	    }
	    if (value.hasResolvedTime()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("resolvedTime ");
		s.print(value.getResolvedTime());
	    }
	    if (value.hasAlarmDesc()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("alarmDesc ");
		s.print("\"" + value.getAlarmDesc().stringValue() + "\"");
	    }
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(WorkStatusRequest value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("serviceId ");
	    s.print(value.getServiceId().longValue());
	    if (value.hasRequestTime()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("requestTime ");
		s.print(value.getRequestTime());
	    }
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(GetAllServiceIdsRequest value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(SendPacketStatsRequest value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("serviceId ");
	    s.print(value.getServiceId().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("network ");
	    s.print(value.getNetwork().longValue());
	    if (value.hasPeriod()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("period ");
		s.print(value.getPeriod().longValue());
	    }
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(ReceivePacketStatsRequest value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("serviceId ");
	    s.print(value.getServiceId().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("network ");
	    s.print(value.getNetwork().longValue());
	    if (value.hasPeriod()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("period ");
		s.print(value.getPeriod().longValue());
	    }
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(CheckCommStatusRequest value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("serviceId ");
	    s.print(value.getServiceId().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("network ");
	    s.print(value.getNetwork().longValue());
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(QueryHostMngRequest value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(SetHostMngRequest value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    if (value.hasCenterIP()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("centerIP ");
		printValue(value.getCenterIP(), s);
	    }
	    if (value.hasAuthPort()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("authPort ");
		s.print(value.getAuthPort().longValue());
	    }
	    if (value.hasAlarmPort()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("alarmPort ");
		s.print(value.getAlarmPort().longValue());
	    }
	    if (value.hasCertMngPort()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("certMngPort ");
		s.print(value.getCertMngPort().longValue());
	    }
	    if (value.hasSgPort()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("sgPort ");
		s.print(value.getSgPort().longValue());
	    }
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(QuerySourceDeviceRequest value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("serviceId ");
	    s.print(value.getServiceId().longValue());
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(SetSourceDeviceRequest value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("serviceId ");
	    s.print(value.getServiceId().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("sourcedevices ");
	    printValue(value.getSourcedevices(), s);
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(IPAddress value, PrintStream s)
    {
	
	switch (value.getChosenFlag()) {
	case IPAddress.ipV4_chosen:
	    s.print("ipV4 : ");
	    s.print(((IPv4Address)value.getChosenValue()));
	    break;
	case IPAddress.ipV6_chosen:
	    s.print("ipV6 : ");
	    s.print(((IPv6Address)value.getChosenValue()));
	    break;
	default:
	    s.print("<unknown choice>");
	}
    }
    public static void printValue(AddServiceRequest value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("displayname ");
	    s.print(value.getDisplayname());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("network ");
	    s.print(value.getNetwork().longValue());
	    if (value.hasProxyIp()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("proxyIp ");
		printValue(value.getProxyIp(), s);
	    }
	    if (value.hasProxyPort()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("proxyPort ");
		s.print(value.getProxyPort().longValue());
	    }
	    if (value.hasServerIp()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("serverIp ");
		printValue(value.getServerIp(), s);
	    }
	    if (value.hasServerPort()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("serverPort ");
		s.print(value.getServerPort().longValue());
	    }
	    if (value.hasContentKeyCheck()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("contentKeyCheck ");
		s.print(value.getContentKeyCheck());
	    }
	    if (value.hasProtocolFilter()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("protocolFilter ");
		s.print(value.getProtocolFilter());
	    }
	    if (value.hasUnpassDeal()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("unpassDeal ");
		s.print(value.getUnpassDeal().longValue());
	    }
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(UpdateServiceRequest value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("serviceId ");
	    s.print(value.getServiceId().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("network ");
	    s.print(value.getNetwork().longValue());
	    if (value.hasProxyIp()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("proxyIp ");
		printValue(value.getProxyIp(), s);
	    }
	    if (value.hasProxyPort()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("proxyPort ");
		s.print(value.getProxyPort().longValue());
	    }
	    if (value.hasServerIp()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("serverIp ");
		printValue(value.getServerIp(), s);
	    }
	    if (value.hasServerPort()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("serverPort ");
		s.print(value.getServerPort().longValue());
	    }
	    if (value.hasContentKeyCheck()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("contentKeyCheck ");
		s.print(value.getContentKeyCheck());
	    }
	    if (value.hasProtocolFilter()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("protocolFilter ");
		s.print(value.getProtocolFilter());
	    }
	    if (value.hasUnpassDeal()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("unpassDeal ");
		s.print(value.getUnpassDeal().longValue());
	    }
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(DeleteServiceRequest value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("messageType ");
	    s.print(value.getMessageType().longValue());
	    s.print(",");
	    newline(s, indentlevel);
	    s.print("serviceId ");
	    s.print(value.getServiceId().longValue());
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(SetSourceDeviceRequest.Sourcedevices value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    for (int _index = 0; _index < value.getSize(); _index++) {
		newline(s, indentlevel);
		printValue(value.get(_index), s);
		if (_index + 1 < value.getSize())
		    s.print(",");
	    }
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    public static void printValue(SourceDevice value, PrintStream s)
    {
	{
	    s.print("{");
	    ++indentlevel;
	    newline(s, indentlevel);
	    s.print("ipAddress ");
	    printValue(value.getIpAddress(), s);
	    if (value.hasPort()) {
		s.print(",");
		newline(s, indentlevel);
		s.print("port ");
		s.print(value.getPort());
	    }
	    newline(s, --indentlevel);
	    s.print("}");
	}
    }
    
    public static int encodeDecodeAndPrint(MessageRequestFrame value, int run)
    {
	Coder coder = com.unimas.asn.servicemanager.Servicemanager.getDefaultCoder();
	ByteArrayInputStream source;
	ByteArrayOutputStream sink;
	byte[] encoding = null;
	boolean passed = true;
	
	/* Print input value using AbstractData.toString() method*/
	System.out.println("\n--------------- Test run " + run + "---------------");
	System.out.println("\nEncoder input value:\n");
	System.out.print(value);
	
	/* Set coder properties */
	coder.enableEncoderDebugging();
	coder.enableDecoderDebugging();
	coder.enableEncoderConstraints();
	coder.enableDecoderConstraints();
	coder.enableAutomaticEncoding();
	coder.enableAutomaticDecoding();
	coder.enableContainedValueEncoding();
	coder.enableContainedValueDecoding();
	
	/* Encode the value */
	sink = new ByteArrayOutputStream();
	try {
	    System.out.print("\n\tTracing Information from Encoder...\n\n");
	    coder.encode(value, sink);
	    encoding = sink.toByteArray();
	    System.out.print("\nPDU successfully encoded, in " + encoding.length + " bytes:\n");
	    
	    if ((coder instanceof XERCoder)
		|| (coder instanceof CXERCoder)
		|| (coder instanceof EXERCoder)) {
		System.out.write(encoding, 0, encoding.length);
	    } else {
		HexTool.printHex(encoding);
	    }
	} catch(EncodeFailedException e) {
	    System.out.println("Encoding failed with return code = " + e.getReason());
	    System.out.print(e);
	    passed = false;
	} catch(EncodeNotSupportedException e) {
	    System.out.println("Encoding not supported for the value");
	    System.out.print(e);
	    passed = false;
	}
	
	if (!passed)
	    return 1;
	
	/* Decode the PDU that was just encoded */
	source = new ByteArrayInputStream(encoding);
	MessageRequestFrame decoded = null;
	try {
	    System.out.print("\n\tTracing Information from Decoder...\n\n");
	    decoded = (MessageRequestFrame)coder.decode(source, value);
	    System.out.print("\nPDU successfully decoded.\n");
	} catch (DecodeFailedException e) {
	    System.out.println("Decoding failed with return code = " + e.getReason());
	    System.out.print(e);
	    passed = false;
	} catch (DecodeNotSupportedException e) {
	    System.out.println("Decoding not supported for the value");
	    System.out.print(e);
	    passed = false;
	}
	
	if (!passed)
	    return 1;
	/* Print decoded value using sample printValue() method */
	System.out.print("\n\tDecoded PDU...\n\n");
	printValue(decoded, System.out);
	System.out.print("\n");
	
	return 0;
    }
    
    public static void main(String[] arg)
    {
	int run = 0;
	int failures = 0;
	
	failures += encodeDecodeAndPrint(createSampleValue(), ++run);
	newline(System.out, 0);
	
	if (failures > 0)
	    System.out.println(failures + " values failed.");
	else
	    System.out.println("All values encoded and decoded successfully.");
    }
    
}

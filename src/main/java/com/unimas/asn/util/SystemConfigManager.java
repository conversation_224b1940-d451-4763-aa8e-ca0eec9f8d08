package com.unimas.asn.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Enumeration;
import java.util.Properties;

/**
 * 系统配置管理工具类
 * 用于管理网卡配置、端口配置和应用配置文件
 */
public class SystemConfigManager {
    private static final Logger logger = LoggerFactory.getLogger(SystemConfigManager.class);
    
    private static final String APPLICATION_PROPERTIES_PATH = "/etc/unimas/tomcat/conf/application.properties";
    private static final String NETWORK_INTERFACES_PATH = "/etc/network/interfaces";
    private static final String NETWORK_INTERFACES_BACKUP = "/etc/network/interfaces.backup";
    
    /**
     * 获取当前eth0网卡的IP地址
     * @return eth0的IP地址，如果获取失败返回null
     */
    public static String getCurrentEth0IP() {
        try {
            NetworkInterface eth0 = NetworkInterface.getByName("eth0");
            if (eth0 != null) {
                Enumeration<InetAddress> addresses = eth0.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress addr = addresses.nextElement();
                    if (!addr.isLoopbackAddress() && addr.getAddress().length == 4) {
                        return addr.getHostAddress();
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Failed to get current eth0 IP address", e);
        }
        return null;
    }

    /**
     * 获取当前eth1网卡的IP地址
     * @return eth1的IP地址，如果获取失败返回null
     */
    public static String getCurrentEth1IP() {
        try {
            NetworkInterface eth1 = NetworkInterface.getByName("eth1");
            if (eth1 != null) {
                Enumeration<InetAddress> addresses = eth1.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress addr = addresses.nextElement();
                    if (!addr.isLoopbackAddress() && addr.getAddress().length == 4) {
                        return addr.getHostAddress();
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Failed to get current eth1 IP address", e);
        }
        return null;
    }
    
    /**
     * 获取当前监听端口（从配置文件读取）
     * @return 当前监听端口
     */
    public static int getCurrentManagementPort() {
        try (InputStream is = new FileInputStream(APPLICATION_PROPERTIES_PATH)) {
            Properties props = new Properties();
            props.load(is);
            String portStr = props.getProperty("server.port", "8080");
            return Integer.parseInt(portStr);
        } catch (Exception e) {
            logger.warn("Failed to read server.port from application.properties, using default 8080", e);
            return 8080;
        }
    }

    /**
     * 获取当前监听地址（从配置文件读取）
     * @return 当前监听地址
     */
    public static String getCurrentManagementAddress() {
        try (InputStream is = new FileInputStream(APPLICATION_PROPERTIES_PATH)) {
            Properties props = new Properties();
            props.load(is);
            return props.getProperty("server.address", "0.0.0.0");
        } catch (Exception e) {
            logger.warn("Failed to read server.address from application.properties, using default 0.0.0.0", e);
            return "0.0.0.0";
        }
    }
    
    /**
     * 从application.properties文件读取上位机配置
     * @return 包含centerIP和centerPort的Properties对象
     */
    public static Properties getCurrentCenterConfig() {
        Properties props = new Properties();
        try (InputStream is = new FileInputStream(APPLICATION_PROPERTIES_PATH)) {
            props.load(is);
        } catch (Exception e) {
            logger.error("Failed to read application.properties", e);
            // 设置默认值
            props.setProperty("alarm.report.destination.ip", "127.0.0.1");
            props.setProperty("alarm.report.destination.port", "8080");
        }
        return props;
    }
    
    /**
     * 修改eth0网卡的完整网络配置（Debian系统）
     * @param ip IP地址
     * @param subnetMask 子网掩码
     * @param gateway 网关（可选）
     * @param route 路由（可选）
     * @return 是否修改成功
     */
    public static boolean updateEth0NetworkConfig(String ip, String subnetMask, String gateway, String route) {
        try {
            logger.info("Updating eth0 network config - IP: {}, SubnetMask: {}, Gateway: {}, Route: {}",
                    ip, subnetMask, gateway, route);

            // 备份原始配置文件
            Files.copy(Paths.get(NETWORK_INTERFACES_PATH),
                      Paths.get(NETWORK_INTERFACES_BACKUP),
                      StandardCopyOption.REPLACE_EXISTING);

            // 读取当前配置文件
            String content = new String(Files.readAllBytes(Paths.get(NETWORK_INTERFACES_PATH)));

            // 更新eth0配置
            String updatedContent = updateNetworkInterfacesContentWithGateway(content, "eth0", ip, subnetMask, gateway, route);

            // 写入新配置
            Files.write(Paths.get(NETWORK_INTERFACES_PATH), updatedContent.getBytes());

            logger.info("Successfully updated eth0 network configuration to persistent storage");
            return true;

        } catch (Exception e) {
            logger.error("Failed to update eth0 network configuration", e);
            // 尝试恢复备份
            try {
                Files.copy(Paths.get(NETWORK_INTERFACES_BACKUP),
                          Paths.get(NETWORK_INTERFACES_PATH),
                          StandardCopyOption.REPLACE_EXISTING);
            } catch (Exception restoreEx) {
                logger.error("Failed to restore network interfaces backup", restoreEx);
            }
            return false;
        }
    }

    /**
     * 修改eth0网卡的IP地址（Debian系统）
     * @param newIP 新的IP地址
     * @return 是否修改成功
     */
    public static boolean updateEth0IP(String newIP) {
        return updateEth0NetworkConfig(newIP, "*************", null, null);
    }

    /**
     * 修改eth1网卡的IP地址（Debian系统）
     * @param newIP 新的IP地址
     * @return 是否修改成功
     */
    public static boolean updateEth1IP(String newIP) {
        try {
            logger.info("Updating eth1 IP address to: {}", newIP);

            // 备份原始配置文件
            Files.copy(Paths.get(NETWORK_INTERFACES_PATH),
                      Paths.get(NETWORK_INTERFACES_BACKUP),
                      StandardCopyOption.REPLACE_EXISTING);

            // 读取当前配置文件
            String content = new String(Files.readAllBytes(Paths.get(NETWORK_INTERFACES_PATH)));

            // 更新eth1配置
            String updatedContent = updateNetworkInterfacesContent(content, "eth1", newIP);

            // 写入新配置
            Files.write(Paths.get(NETWORK_INTERFACES_PATH), updatedContent.getBytes());

            // 重启网络服务
            executeCommand("systemctl restart networking");

            logger.info("Successfully updated eth1 IP address to: {}", newIP);
            return true;

        } catch (Exception e) {
            logger.error("Failed to update eth1 IP address", e);
            // 尝试恢复备份
            try {
                Files.copy(Paths.get(NETWORK_INTERFACES_BACKUP),
                          Paths.get(NETWORK_INTERFACES_PATH),
                          StandardCopyOption.REPLACE_EXISTING);
            } catch (Exception restoreEx) {
                logger.error("Failed to restore network interfaces backup", restoreEx);
            }
            return false;
        }
    }
    
    /**
     * 更新网络接口配置文件内容
     * @param content 原始内容
     * @param interfaceName 网卡名称（eth0或eth1）
     * @param newIP 新IP地址
     * @return 更新后的内容
     */
    private static String updateNetworkInterfacesContent(String content, String interfaceName, String newIP) {
        // 简单的替换策略，假设指定网卡配置存在
        // 这里可以根据实际的网络配置格式进行调整
        String[] lines = content.split("\n");
        StringBuilder result = new StringBuilder();
        boolean inTargetSection = false;

        for (String line : lines) {
            if (line.trim().startsWith("iface " + interfaceName)) {
                inTargetSection = true;
                result.append(line).append("\n");
            } else if (inTargetSection && line.trim().startsWith("address")) {
                result.append("    address ").append(newIP).append("\n");
                inTargetSection = false;
            } else if (inTargetSection && line.trim().startsWith("iface") && !line.contains(interfaceName)) {
                inTargetSection = false;
                result.append(line).append("\n");
            } else {
                result.append(line).append("\n");
            }
        }

        return result.toString();
    }

    /**
     * 更新网络接口配置文件内容（兼容旧方法）
     * @param content 原始内容
     * @param newIP 新IP地址
     * @return 更新后的内容
     */
    private static String updateNetworkInterfacesContent(String content, String newIP) {
        return updateNetworkInterfacesContent(content, "eth1", newIP);
    }
    
    /**
     * 更新application.properties文件中的上位机配置
     * @param centerIP 新的上位机IP
     * @param centerPort 新的上位机端口
     * @return 是否更新成功
     */
    public static boolean updateCenterConfig(String centerIP, int centerPort) {
        try {
            logger.info("Updating center config - IP: {}, Port: {}", centerIP, centerPort);

            Properties props = new Properties();

            // 读取现有配置
            try (InputStream is = new FileInputStream(APPLICATION_PROPERTIES_PATH)) {
                props.load(is);
            }

            // 更新配置
            props.setProperty("alarm.report.destination.ip", centerIP);
            props.setProperty("alarm.report.destination.port", String.valueOf(centerPort));

            // 写入配置文件
            try (OutputStream os = new FileOutputStream(APPLICATION_PROPERTIES_PATH)) {
                props.store(os, "Updated by SetMngCfgRequest");
            }

            logger.info("Successfully updated center config");
            return true;

        } catch (Exception e) {
            logger.error("Failed to update center config", e);
            return false;
        }
    }

    /**
     * 更新application.properties文件中的上位机配置，并启用报警上报
     * @param centerIP 新的上位机IP
     * @param centerPort 新的上位机端口
     * @return 是否更新成功
     */
    public static boolean updateCenterConfigWithAlarmEnabled(String centerIP, int centerPort) {
        try {
            logger.info("Updating center config with alarm enabled - IP: {}, Port: {}", centerIP, centerPort);

            Properties props = new Properties();

            // 读取现有配置
            try (InputStream is = new FileInputStream(APPLICATION_PROPERTIES_PATH)) {
                props.load(is);
            }

            // 更新配置
            props.setProperty("alarm.report.destination.ip", centerIP);
            props.setProperty("alarm.report.destination.port", String.valueOf(centerPort));
            props.setProperty("alarm.report.enabled", "true");

            // 写入配置文件
            try (OutputStream os = new FileOutputStream(APPLICATION_PROPERTIES_PATH)) {
                props.store(os, "Updated by SetMngCfgRequest with alarm enabled");
            }

            logger.info("Successfully updated center config with alarm enabled");
            return true;

        } catch (Exception e) {
            logger.error("Failed to update center config with alarm enabled", e);
            return false;
        }
    }

    /**
     * 更新application.properties文件中的服务器监听配置
     * @param managementAddress 新的监听地址
     * @param managementPort 新的监听端口
     * @return 是否更新成功
     */
    public static boolean updateManagementConfig(String managementAddress, int managementPort) {
        try {
            logger.info("Updating management config - Address: {}, Port: {}", managementAddress, managementPort);

            Properties props = new Properties();

            // 读取现有配置
            try (InputStream is = new FileInputStream(APPLICATION_PROPERTIES_PATH)) {
                props.load(is);
            }

            // 更新配置
            props.setProperty("server.address", managementAddress);
            props.setProperty("server.port", String.valueOf(managementPort));

            // 写入配置文件
            try (OutputStream os = new FileOutputStream(APPLICATION_PROPERTIES_PATH)) {
                props.store(os, "Updated by SetMngCfgRequest");
            }

            logger.info("Successfully updated management config");
            return true;

        } catch (Exception e) {
            logger.error("Failed to update management config", e);
            return false;
        }
    }
    
    /**
     * 执行系统命令
     * @param command 要执行的命令
     * @return 命令执行结果
     */
    private static boolean executeCommand(String command) {
        try {
            logger.info("Executing command: {}", command);
            Process process = Runtime.getRuntime().exec(command);
            int exitCode = process.waitFor();
            
            if (exitCode == 0) {
                logger.info("Command executed successfully: {}", command);
                return true;
            } else {
                logger.error("Command failed with exit code {}: {}", exitCode, command);
                return false;
            }
        } catch (Exception e) {
            logger.error("Failed to execute command: {}", command, e);
            return false;
        }
    }
    
    /**
     * 检查IP地址格式是否有效
     * @param ip IP地址字符串
     * @return 是否有效
     */
    public static boolean isValidIP(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }
        
        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }
        
        try {
            for (String part : parts) {
                int value = Integer.parseInt(part);
                if (value < 0 || value > 255) {
                    return false;
                }
            }
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * 检查端口号是否有效
     * @param port 端口号
     * @return 是否有效
     */
    public static boolean isValidPort(int port) {
        return port >= 1025 && port <= 65535;
    }
}

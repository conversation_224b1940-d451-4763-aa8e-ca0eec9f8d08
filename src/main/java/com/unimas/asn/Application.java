package com.unimas.asn;

import com.unimas.asn.http.CardIdServlet;
import com.unimas.asn.http.CertManagerServlet;
import com.unimas.asn.util.Constant;
import com.unimas.asn.util.SystemConfigManager;
import org.eclipse.jetty.server.Server;
import org.eclipse.jetty.server.ServerConnector;
import org.eclipse.jetty.servlet.ServletContextHandler;
import org.eclipse.jetty.servlet.ServletHolder;
import org.eclipse.jetty.util.log.Log;
import org.eclipse.jetty.util.log.StdErrLog;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.unimas.asn.http.MessageServlet;

import java.io.File;

/**
 * 主应用程序类
 */
public class Application {
    private static final Logger logger = LoggerFactory.getLogger(Application.class);

    public static Server createServer(String address, int port) {
        // 创建服务器
        Server server = new Server();

        // 创建ServerConnector并设置地址和端口
        ServerConnector connector = new ServerConnector(server);
        connector.setHost(address);
        connector.setPort(port);
        server.addConnector(connector);

        // 创建Servlet上下文
        ServletContextHandler context = new ServletContextHandler(ServletContextHandler.SESSIONS);
        context.setContextPath("/");
        server.setHandler(context);

        // 添加消息处理Servlet
        context.addServlet(new ServletHolder(new MessageServlet()), "/sg");

        // 添加证书管理Servlet
        context.addServlet(new ServletHolder(new CertManagerServlet()), "/certmng");
        context.addServlet(new ServletHolder(new CertManagerServlet()), "/auth");

        // 添加证书ID查询Servlet
        context.addServlet(new ServletHolder(new CardIdServlet()), "/cardid");
        return server;
    }

    public static Server createServer(int port) {
        return createServer("0.0.0.0", port);
    }

    public static void main(String[] args) {
        try {
            // 关闭Jetty调试日志
            Log.setLog(new StdErrLog());
            ((StdErrLog) Log.getLog()).setLevel(StdErrLog.LEVEL_WARN);

            // 从配置文件读取监听地址和端口
            String managementAddress = SystemConfigManager.getCurrentManagementAddress();
            int managementPort = SystemConfigManager.getCurrentManagementPort();

            logger.info("Reading server configuration from application.properties - Address: {}, Port: {}",
                    managementAddress, managementPort);

            // 创建服务器
            Server server = createServer(managementAddress, managementPort);

            // 启动服务器
            server.start();
            logger.info("Server started on {}:{}", managementAddress, managementPort);
//            if(!new File(Constant.SERVICE_PRE_PATH+"alarm_stop").exists()){
                AlarmMonitoringService monitoringService = new AlarmMonitoringService();
                monitoringService.start();

                logger.info("Alarm monitoring service started.");
                // Add shutdown hook to stop the service gracefully
                Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                    logger.info("Shutdown signal received. Stopping monitoring service...");
                    monitoringService.stop();
                    logger.info("Monitoring service stopped. Exiting application.");
                }));
//            }
            // 等待服务器停止
            server.join();

        } catch (Exception e) {
            logger.error("Server error", e);
            System.exit(1);
        }
    }
}
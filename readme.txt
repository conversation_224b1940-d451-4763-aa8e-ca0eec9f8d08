单向交互模块应用接口
1  通信接口
1.1  通用要求
应符合以下要求：
a)	接口及请求类型：HTTP POST；
b)	接口传递参数：
——	HTTP Content-Type： application/octet-stream；
——	HTTP request body与HTTP response body中的消息结构符合规定。
1.2  服务URL
通过不同URL定位业务模块：
a）身份认证URL，暂定“http://上位机IP:Port/auth”，由安全交互模块主动发起请求，上位机提供服务；
b）告警上报URL，暂定“http://上位机IP:Port/alarm”，由光闸主动发起请求。
c）证书管理URL，暂定“http://安全交互模块IP:Port/certmng”，由上位机主动发起请求；
d）业务管理URL，暂定“http://安全交互模块IP:Port/sg”，由上位机主动发起请求。
2　消息数据结构
2.1　编码规则
按GB/T 16262.1规定的ASN.1对消息结构及数据结构进行描述，按ISO/IEC 8825-7规定的正则八位字节编码规则COER（Canonical Octet Encoding Rules）进行编码。
2.2　请求消息结构
MessageRequestFrame ::= SEQUENCE {
version		Uint8(1),
content		CHOICE {
setInterfaceIpRequest       SetInterfaceIpRequest,
serviceConfigRequest		ServiceConfigRequest,
serviceControlRequest  		ServiceControlRequest,
serviceStatusQueryRequest  	ServiceStatusQueryRequest,
serviceConfigQueryRequest  	ServiceConfigQueryRequest, 
alarmReportRequest  		AlarmReportRequest,
workStatusRequest 			WorkStatusRequest,
getAllServiceIdsRequest		GetAllServiceIdsRequest, 	 sendPacketStatsRequest 		SendPacketStatsRequest, 
receivePacketStatsRequest	ReceivePacketStatsRequest,  	checkCommStatusRequest  	CheckCommStatusRequest, 
queryHostMngRequest         QueryHostMngRequest,
setHostMngRequest          SetHostMngRequest,
querySourceDeviceRequest   QuerySourceDeviceRequest,
setSourceDeviceRequest     SetSourceDeviceRequest,
queryTargetDeviceRequest     QueryTargetDeviceRequest,
...
}
}
此结构规定了HTTP request body中的消息结构，消息结构内数据字段定义如下：
——	version：请求消息结构的版本号，此字段应设置为“1”；
——	setInterfaceIpRequest：网口配置请求数据，数据结构应符合4.1.1规定；
——	serviceConfigRequest：服务配置请求数据，数据结构应符合4.2.1规定；
——	serviceControlRequest：服务启停请求数据，数据结构应符合4.3.1规定；
——	serviceStatusQueryRequest：服务状态查询请求数据，数据结构应符合4.4.1规定；
——	serviceConfigQueryRequest：服务配置查询请求数据，数据结构应符合4.5.1规定；
——	alarmReportRequest：告警上报请求数据，数据结构应符合4.6.1规定；
——	workStatusRequest：工作状态请求，数据结构应符合4.7.1规定；
——	getAllServiceIdsRequest：获取所有服务号列表，数据结构应符合4.8.1规定；
——	sendPacketStatsRequest：查询发包信息，数据结构应符合4.9.1规定；
——	receivePacketStatsRequest：查询收包信息，数据结构应符合4.10.1规定；
——	checkCommStatusRequest：查询通信状态，数据结构应符合4.11.1规定；
——	queryHostMngRequest：上位机ip及端口查询，数据结构应符合4.12.1规定；
——	setHostMngRequest： 上位机ip及端口管理，数据结构应符合4.12.3规定；
——	querySourceDeviceRequest：源设备ip及端口查询，数据结构应符合4.13.1规定；
——	setSourceDeviceRequest：源设备ip及端口管理，数据结构应符合4.13.3规定；
——	queryTargetDeviceRequest：目标设备ip及端口查询，数据结构应符合4.14.1规定。
2.3　响应消息结构
MessageResponseFrame ::= SEQUENCE {
version		Uint8(1),
content		CHOICE {
setInterfaceIpResponse      SetInterfaceIpResponse
serviceConfigResponse  		ServiceConfigResponse,
serviceControResponse  		ServiceControlResponse,
serviceStatusQueryResponse	ServiceStatusQueryResponse,
serviceConfigQueryResponse	ServiceConfigQueryResponse, 
alarmReportResponse  		AlarmReportResponse,
workStatusResponse 			WorkStatusResponse,
getAllServiceIdsResponse 	GetAllServiceIdsResponse,
sendPacketStatsResponse		SendPacketStatsResponse, 
receivePacketStatsResponse 	ReceivePacketStatsResponse,
checkCommStatusResponse 	CheckCommStatusResponse,
queryHostMngResponse        QueryHostMngResponse,
setHostMngResponse          SetHostMngResponse,
querySourceDeviceResponse   QuerySourceDeviceResponse,
setSourceDeviceResponse     SetSourceDeviceResponse,
queryTargetDeviceResponse   QueryTargetDeviceResponse,
error						ErrorResponse,
...
}
}
此结构规定了HTTP response body中的消息结构，消息结构内数据字段定义如下：
——	version：响应消息结构的版本号，此字段应设置为“1”；
——	setInterfaceIpResponse：网口配置响应数据，数据结构应符合4.1.2规定；
——	serviceConfigResponse：服务配置响应数据，数据结构应符合4.2.2规定；
——	serviceControlResponse：服务启停响应数据，数据结构应符合4.3.2规定；
——	serviceStatusQueryResponse：服务状态查询响应数据，数据结构应符合4.4.2规定；
——	serviceConfigQueryResponse：服务配置查询响应数据，数据结构应符合4.5.2规定；
——	alarmReportResponse：告警上报响应数据，数据结构应符合4.6.2规定；
——	workStatusResponse：工作状态查询响应数据，数据结构应符合4.7.2规定；
——	getAllServiceIdsResponse：获取所有服务号列表响应数据，数据结构应符合4.8.2规定；
——	sendPacketStatsResponse：查询发包信息响应数据，数据结构应符合4.9.2规定；
——	receivePacketStatsResponse：查询收包信息响应数据，数据结构应符合4.10.2规定；
——	checkCommStatusResponse：查询通信状态响应数据，数据结构应符合4.11.2规定。
——	queryHostMngResponse： 上位机查询响应数据，数据结构应符合4.12.2规定；
——	setHostMngResponse： 上位机管理响应数据，数据结构应符合4.12.4规定；
——	querySourceDeviceResponse：源设备查询响应数据，数据结构应符合4.13.2规定；
——	setSourceDeviceResponse：源设备管理响应数据，数据结构应符合4.13.4规定；
——	queryTargetDeviceResponse：目标设备查询响应数据，数据结构应符合4.14.2规定；
——	error：异常响应数据，数据结构应符合5.1规定。
3 安全与认证
参考国标“设施数字身份”，由交互模块主动发起，双向身份认证成功后，使用对称密钥进行加密通信（暂时保留，下阶段实施）（长连接一次操作，下阶段实施）。
4 业务消息定义
4.1  网口配置
4.1.1  请求数据结构
SetInterfaceIpRequest ::= SEQUENCE {
messageType    ContentMessageType,  	
interfaceType  InterfaceType,     
ipAddress      IPAddress,   
    subnetMask     IPAddress,           
gateway        IPAddress OPTIONAL,
iproute        IPAddress OPTIONAL,
}
此数据结构内字段定义如下：
网关和路由各自配置一个
——messageType：消息类型；
——interfaceType：网口类型枚举，management=管理口，business=业务口；
——ipAddress：请求中待配置的IP地址；
——subnetMask：请求中待配置的子网掩码；
——gateway：请求中待配置的网关地址；
——iproute：请求中待配置的路由地址。
4.1.2  响应数据结构
SetInterfaceIpResponse ::= SEQUENCE {
messageType       ContentMessageType,   
interfaceType     InterfaceType,       
    currentIpAddress  IPAddress,            
currentSubnetMask IPAddress,           
currentGateway    IPAddress OPTIONAL,
curIproute        IPAddress OPTIONAL,    
    result            INTEGER (0..1000)     
}
此数据结构内字段定义如下：
——messageType：消息类型；
——interfaceType：网口类型枚举，management=管理口，business=业务口；
——currentIpAddress：响应中此网口当前已配置的IP地址；
——currentSubnetMask：响应中此网口当前生效的子网掩码；
——currentGateway：响应中此网口当前生效的网关地址；
——curIproute：响应中此网口当前生效的路由地址；
——result：执行结果，0表示成功，1表示错误。
4.2 服务配置
4.2.1  请求数据结构
ServiceConfigRequest ::= CHOICE {
addServiceRequest 		AddServiceRequest,
updateServiceRequest 	UpdateServiceRequest,
deleteServiceRequest 	DeleteServiceRequest,
...   
}
此数据结构内字段定义如下：
——	addServiceRequest：服务新增；
——	updateServiceRequest：服务修改；
——	deleteServiceRequest：服务删除。
4.2.2　响应数据结构
ServiceConfigResponse ::= SEQUENCE {
messageType		ContentMessageType,
serviceId		ServiceId,
...   
}
此数据结构内字段定义如下：
——	messageType：消息类型；
——	serviceId：服务号，唯一标识。
4.3  服务启停
4.3.1  请求数据结构
ServiceControlRequest ::= SEQUENCE {
messageType			ContentMessageType,
serviceId       	ServiceId,
serviceStartOrStop	ServiceControlAction
...   
}
此数据结构内字段定义如下：
——	messageType：消息类型；
——	serviceStartOrStop：启停命令；
——	serviceId：服务号。
4.3.2　响应数据结构
ServiceControlResponse ::= SEQUENCE {
messageType 		ContentMessageType,
serviceId       	ServiceId
serviceStartOrStop 	ServiceControlAction,
...   
}
此数据结构内字段定义如下：
——	messageType：消息类型；
——	serviceId：服务号。
4.4  服务状态查询
4.4.1  请求数据结构
ServiceStatusQueryRequest ::= SEQUENCE {
messageType  	ContentMessageType,
serviceId    	ServiceId,
...   
}
此数据结构内字段定义如下：
——	messageType：消息类型；
——	serviceId：服务号。
4.4.2　响应数据结构
ServiceStatusQueryResponse ::= SEQUENCE {
messageType 	ContentMessageType,
serviceId     	ServiceId,
serviceStatus 	ServiceStatus，
...   
}
此数据结构内字段定义如下：
——	messageType：消息类型；
——	serviceId：服务号；
——	serviceStatus：服务运行状态。
4.5  服务配置查询
4.5.1  请求数据结构
ServiceConfigQueryRequest ::= SEQUENCE {
messageType   	ContentMessageType,
serviceId     	ServiceId,
network    		Network   
}
此数据结构内字段定义如下：
——	messageType：消息类型；
——	serviceId：服务号；
——	network：网段。
4.5.2　响应数据结构
ServiceConfigQueryResponse ::= SEQUENCE {
messageType  		ContentMessageType,
serviceId     		ServiceId,
displayname   		DisplayName,
network    			Network，        
proxyIp     		IPAddress OPTIONAL,
proxyPort  			PortNumber OPTIONAL,
serverIp    		IPAddress OPTIONAL,
serverPort  		PortNumber OPTIONAL,
contentKeyCheck		ContentKeyCheck OPTIONAL,
protocolFilter   	ProtocolFilter  OPTIONAL,
unpassDeal     		PermissionState  OPTIONAL,
...   
}
此数据结构内字段定义如下：
——	messageType：消息类型；
——	serviceId：服务号；
——	displayname：服务名称；
——	network: 网段；
——	proxyIp：备用监听指定ip；
——	proxyPort:业务口端口；
——	serverIp:目标服务器ip地址,network=1 显示；
——	serverPort:目标服务器端口,network=1 显示；
——	contentKeyCheck：关键字检测,network=0 显示；
——	protocolFilter：协议过滤,network=0显示；
——	unpassDeal：告警后处理,network=0显示。
4.6  告警上报
由上位机提供HTTP服务，设备发起调用请求。
4.6.1  请求数据结构
AlarmReportRequest ::= SEQUENCE {
messageType  	ContentMessageType,
serviceId     	ServiceId,
alarmType     	AlarmType，
alarmCode    	AlarmCode，
alarmStatus  	AlarmStatus，
happenTime   	Uint64 OPTIONAL，
resolvedTime  	Uint64 OPTIONAL，
alarmDesc     	IA5String (SIZE(0..200)) OPTIONAL，
...   
}
此数据结构内字段定义如下：
——	messageType：消息类型；
——	serviceId：服务号， 服务号0表示系统报警；
——	alarmType：报警类型；
——	alarmCode：报警代码；
——	alarmStatus：报警状态；
——	happenTime：报警产生时间 alarmStatus=0；
——	resolvedTime：报警解除时间 alarmStatus=1；
——	alarmDesc：报警详细描述 alarmStatus=0。
4.6.2　响应数据结构
AlarmReportResponse ::= SEQUENCE {
messageType   	ContentMessageType,
serviceId     	ServiceId,
...   
}
此数据结构内字段定义如下：
——	messageType：消息类型；
——	serviceId：服务号。
4.7 工作状态查询
4.7.1请求数据结构
WorkStatusRequest ::= SEQUENCE {
messageType  	ContentMessageType,
serviceId     	ServiceId,	
requestTime   	Uint64 OPTIONAL，--查询时间，上位机本地时间
...   
}
此数据结构内字段定义如下：
——	messageType：消息类型；
——	serviceId：服务号。
——	requestTime：查询时间。
4.7.2响应数据结构
WorkStatusResponse ::= SEQUENCE {
messageType   	ContentMessageType,
serviceId     	ServiceId,
sendPkgNumToday Uint32,		--发送数据包数量
sendPkgSizeToday Uint32,	--发送数据包大小
recvPkgNumToday Uint32,		--接收数据包数量
recvPkgSizeToday Uint32,	--接收数据包大小
其他工作状态信息……
devTime   	Uint64 OPTIONAL，--应答时间，设备本地时间
...   
}
此数据结构内字段定义如下：
——	messageType：消息类型；
——	serviceId：服务号；
——	sendPkgNumToday：发送数据包数量；
——	sendPkgSizeToday：发送数据包大小；
——	recvPkgNumToday：接收数据包数量；
——	recvPkgSizeToday：接收数据包大小；
——	devTime：应答时间，设备本地时间。

4.8 获取所有服务号列表
4.8.1请求数据结构
GetAllServiceIdsRequest ::= SEQUENCE {
    messageType     ContentMessageType
}

此数据结构内字段定义如下：
——	messageType：消息类型。
4.8.2响应数据结构
GetAllServiceIdsResponse ::= SEQUENCE {
    messageType     ContentMessageType,
    serviceIds      SEQUENCE OF ServiceId
}

此数据结构内字段定义如下：
——	messageType：消息类型；
——	serviceIds：所有可用服务号。
4.9 查询发包信息
4.9.1请求数据结构
SendPacketStatsRequest ::= SEQUENCE {
    messageType     ContentMessageType,
    serviceId       ServiceId,
network         Network，
    period          Uint32 OPTIONAL
}
此数据结构内字段定义如下：
——	messageType：消息类型；
——	serviceId：服务号；
——	network: 网段；
——	period：查询周期（分钟）。
4.9.2响应数据结构
SendPacketStatsResponse ::= SEQUENCE {
    messageType     ContentMessageType,
    serviceId       ServiceId,
network         Network，
period          Uint32 OPTIONAL
    packetStats     SEQUENCE OF PacketStats
}
此数据结构内字段定义如下：
——	messageType：消息类型；
——	serviceId：服务号；
——	network: 网段；
——	period：查询周期（分钟）；
——	packetStats：包统计列表。
4.10 查询收包信息
4.10.1请求数据结构
ReceivePacketStatsRequest ::= SEQUENCE {
    messageType     ContentMessageType,
    serviceId       ServiceId,
network         Network，
    period          Uint32 OPTIONAL
}
此数据结构内字段定义如下：
——	messageType：消息类型；
——	serviceId：服务号；
——	network: 网段；
——	period：查询周期（分钟）。
4.10.2响应数据结构
ReceivePacketStatsResponse ::= SEQUENCE {
    messageType     ContentMessageType,
    serviceId       ServiceId,
network         Network，
period          Uint32 OPTIONAL，
    packetStats     SEQUENCE OF PacketStats 
}
此数据结构内字段定义如下：
——	messageType：消息类型；
——	serviceId：服务号；
——	network: 网段；
——	period：查询周期（分钟）；
——	packetStats：包统计列表。
4.11 查询通信状态
4.11.1请求数据结构
CheckCommStatusRequest ::= SEQUENCE {
    messageType     ContentMessageType,
    serviceId       ServiceId,
network         Network，
}
此数据结构内字段定义如下：
——	messageType：消息类型；
——	serviceId：服务号；
——	network: 网段。
4.11.2响应数据结构
CheckCommStatusResponse ::= SEQUENCE {
    messageType       ContentMessageType,
    serviceId         ServiceId,
network           Network，
isConnected       BOOLEAN,
connectionEventTime    Uint64 
}
此数据结构内字段定义如下：
——	messageType：消息类型；
——	serviceId：服务号；
——	network: 网段；
——	isConnected: 是否连接正常，即为业务口网线是不是通；
——	connectionEventTime: 连接或断开时间戳（isConnected=FALSE 时为断开时间，isConnected=TRUE 时为连接时间）。
4.12 上位机及端口管理
4.12.1请求数据结构
QueryHostMngRequest::= SEQUENCE {
    messageType     ContentMessageType,
}
此数据结构内字段定义如下：
——	messageType：消息类型；
4.12.2响应数据结构
QueryHostMngResponse ::= SEQUENCE {
    messageType         ContentMessageType,  
    centerIP            IPAddress,           
    authPort            PortNumber,          
    alarmPort           PortNumber,          
    certMngPort         PortNumber,          
    sgPort              PortNumber           
}
此数据结构内字段定义如下：
——	messageType：消息类型；
——	centerIP：上位机IP； 
——	authPort：身份认证端口；
——	alarmPort：告警上报端口；
——	certMngPort：证书管理端口；
——	sgPort：业务管理端口。
4.12.3请求数据结构
SetHostMngRequest ::= SEQUENCE {
    messageType         ContentMessageType,  
    centerIP            IPAddress OPTIONAL,          
    authPort            PortNumber OPTIONAL,          
    alarmPort           PortNumber OPTIONAL,          
    certMngPort         PortNumber OPTIONAL,          
    sgPort              PortNumber OPTIONAL          
}
此数据结构内字段定义如下：
把有的信息设置上去
——	messageType：消息类型；
——	centerIP: 上位机IP；
——	authPort：身份认证端口；
——	alarmPort：告警上报端口；
——	certMngPort：证书管理端口；
——	sgPort：业务管理端口。
4.12.4响应数据结构
SetHostMngResponse::= SEQUENCE {
    messageType        ContentMessageType,
	centerIP            IPAddress,          
    authPort            PortNumber,          
    alarmPort           PortNumber,          
    certMngPort         PortNumber,          
    sgPort              PortNumber，
	result              INTEGER (0..1000)
}
此数据结构内字段定义如下：
——	messageType：消息类型；
——	centerIP: 上位机IP；
——	authPort：身份认证端口；
——	alarmPort：告警上报端口；
——	certMngPort：证书管理端口；
——	sgPort：业务管理端口；
——	result：执行结果，0表示成功，1表示错误。
4.13源设备管理
4.13.1请求数据结构
QuerySourceDeviceRequest ::= SEQUENCE {
    messageType       ContentMessageType,    
    serviceId         ServiceId,
}

-- messageType：消息类型；
-- serviceId：服务号。
4.13.2响应数据结构
QuerySourceDeviceResponse ::= SEQUENCE {
    messageType    ContentMessageType,
serviceId      ServiceId,
    currentDevices SEQUENCE OF SourceDevice       
}

-- messageType：消息类型；
-- serviceId：服务号；  
-- currentDevices：响应中返回的所有已生效的源设备ip端口信息列表；
4.13.3请求数据结构
SetSourceDeviceRequest ::= SEQUENCE {
    messageType       ContentMessageType,
serviceId         ServiceId,    
    sourcedevices     SEQUENCE OF SourceDevice  
}

-- messageType：消息类型；
-- serviceId：服务号；
-- sourcedevices ：请求中待配置的源设备列表。
4.13.4响应数据结构
SetSourceDeviceResponse ::= SEQUENCE {
    messageType    ContentMessageType,
    serviceId      ServiceId,
    currentDevices SEQUENCE OF SourceDevice，
    result         INTEGER (0..1000)        
}

-- messageType：消息类型；  
-- serviceId：服务号；
-- currentDevices：响应中返回的所有已生效的源设备ip端口信息列表；
-- result：执行结果，0表示成功，1表示错误。  
5 数据帧及数据元素
5.1  ErrorResponse 数据结构
ErrorResponse ::= SEQUENCE {
messageType		ContentMessageType,
errorState		ProcessErrorState,
...   
}
此数据结构用于被请求方在数据结构无法解析、证书无效、签名验证错误、操作错误等异常情况下的响应处理，数据结构内字段定义如下：
——	messageType：消息类型；
——	errorState：错误或异常类型。
5.2  ContentMessageType 数据结构
ContentMessageType ::= ENUMERATED{
setInterfaceIpService,
addService,
updateService,
deleteService,
controlService,
queryServiceStatus,
queryServiceConfig,
reportAlarm,
queryWorkStatus,
getAllServiceIds,
sendPacketStats,
receivePacketStats,
checkCommStatusService,
hostMngService，
sourceDeviceService,
... 
}
此数据结构内字段定义如下：
——	setInterfaceIpService：网口ip配置；
——	addService：服务配置新增；
——	updateService：服务配置修改；
——	deleteService：服务配置删除；
——	controlService：服务启停控制；
——	queryServiceStatus：服务状态查询；
——	queryServiceConfig：服务配置查询；
——	reportAlarm：告警上报；
——	getAllServiceIds：获取所有服务号列表；
——	sendPacketStats：查询发包信息；
——	receivePacketStats：查询收包信息；
——	checkCommStatusService：查询通信状态；
——	hostMngService：上位机管理服务;
——	sourceDeviceService：源设备管理服务；
5.3  ProcessErrorState 数据结构
ProcessErrorState ::= ENUMERATED{
messageStructureError,
serviceNumLimitError,
displayNameConflictError,
illegalArgumentError,
serviceStatusError,
serviceNotExistError,
illegalOperationError,
sessionTimeout,
...
}
此数据结构内字段定义如下：
——	messageStructureError：消息数据结构无法解析；
——	serviceNumLimitError：服务数已达上限；
——	displayNameConflictError：服务名称冲突；
——	illegalArgumentError：参数非法；
——	serviceStatusError：服务状态异常；
——	serviceNotExistError：服务不存在；
——	illegalOperationError：操作非法；
——	sessionTimeout：会话超时，需重新发起认证。
5.4  AddServiceRequest 数据结构
AddServiceRequest ::= SEQUENCE {
messageType   		ContentMessageType,
displayname   		DisplayName,
network     		Network,
proxyIp             IPAddress OPTIONAL,
proxyPort   		PortNumber,
serverIp            IPAddress,
serverPort   		PortNumber OPTIONAL,
contentKeyCheck   	ContentKeyCheck OPTIONAL,
protocolFilter   	ProtocolFilter OPTIONAL,
unpassDeal     		PermissionState OPTIONAL,
...   
}
此数据结构内字段定义如下：
——	messageType：消息类型；
——	displayname：服务名称；
——	network: 网段；
——	proxyIp：备用监听指定ip；
——	proxyPort：业务口端口；
——	serverIp：目标服务器ip地址,network=1时设置；
——	serverPort：目标服务器端口,network=1时设置；
——	contentKeyCheck：关键字检测,network=0；
——	protocolFilter：协议过滤,network=0；
——	unpassDeal：告警后处理,network=0。
5.5  UpdateServiceRequest 数据结构
UpdateServiceRequest ::= SEQUENCE {
messageType   		ContentMessageType,
serviceId     		ServiceId,
network  			Network,
proxyIp             IPAddress OPTIONAL,
proxyPort   		PortNumber ,
serverIp            IPAddress,
serverPort   		PortNumber OPTIONAL,
contentKeyCheck   	ContentKeyCheck OPTIONAL,
protocolFilter   	ProtocolFilter OPTIONAL,
unpassDeal     		PermissionState OPTIONAL,
...   
}
此数据结构内字段定义如下：
——	messageType：消息类型；
——	serviceId：服务号；
——	Network：网段；
——	proxyIp：备用监听指定ip
——	proxyPort:业务口端口；
——	serverIp:目标服务器ip地址,network=1时设置；
——	serverPort:目标服务器端口,network=1时设置；
——	contentKeyCheck：关键字检测,network=0；
——	protocolFilter：协议过滤,network=0；
——	unpassDeal：告警后处理,network=0。
5.6  DeleteServiceRequest 数据结构
DeleteServiceRequest ::= SEQUENCE {
messageType  	ContentMessageType,
serviceId    	INTEGER (0..32767),
...   
}
此数据结构内字段定义如下：
——	messageType：消息类型；
——	serviceId：服务新增返回的服务号。
5.7  ServiceId 数据结构
ServiceId ::= INTEGER (0..32767)
0：特殊id，
1-100：有效值
5.8  IPAddress 数据结构
IPAddress ::= CHOICE {
ipV4 		IPv4Address,
ipV6 		IPv6Address
}
	IPv4Address 数据结构
IPv4Address ::= OCTET STRING (SIZE(4))
		IPv6Address 数据结构
Ipv6Address ::= OCTET STRING (SIZE(16))
5.9  PortNumber 数据结构
PortNumber ::= INTEGER (1025..65535)
5.10  DisplayName 数据结构
DisplayName ::= OCTET STRING (SIZE (1..40))
5.11  Network 数据结构
Network ::= ENUMERATED{
sender,--发送端；
receiver,--接收端；
...
}
5.12  ProtocolFilter 数据结构
ProtocolFilter ::= BIT STRING { 
							-- 按位定义，每位取值1：开启，0：关闭::
		crcfilter(0),   	--crc16格式过滤；
		asnfilter(1),   	--asn格式过滤；
		                    --Bits 2~7 reserved
	} (SIZE(8,...))
5.13  ContentKeyCheck 数据结构
 	ContentKeyCheck::= BIT STRING { 
							-- 按位定义，每位取值1：开启，0：关闭:
		carcheck(0),   		--机动车牌效验；
		idcheck (1),   		--cn身份证效验；
		                    --Bits 2~7 reserved
	} (SIZE(8,...))
5.14  PermissionState 数据结构
PermissionState ::= ENUMERATED{
allow,			--表示仅报警数据正常传输
forbidden,		--表示报警+数据丢弃不转发
...
}
5.15  ServiceControlAction 数据结构
ServiceControlAction ::= ENUMERATED{
start,			--启动服务；
stop,			--停止服务；
...
}
5.16  AlarmType 数据结构
AlarmType ::= ENUMERATED{
faultAlarm,			--故障报警（可以自动恢复解除）
securityAlarm,			--安全告警（非故障提示性告警）
...
}
5.17  AlarmCode 数据结构
AlarmCode ::= ENUMERATED{
illegalCertificate,		--非许可身份连接（证书非法）；
deviceException,		--设备异常；
channelException,		--通道连接异常；
protocolVerifyFailure,		--协议效验失败；
keywordCheckFailure,		--关键字校验失败；
...
}
5.18  AlarmStatus 数据结构
AlarmStatus ::= ENUMERATED{
alarmRaised,	--报警
alarmCleared,	--报警解除
...
}
5.19  ServiceStatus 数据结构
ServiceStatus ::= ENUMERATED{
running,	--已启动
stopped,	--已停止
...
}
5.20  PacketType 数据结构
PacketType ::= ENUMERATED{
cim(1),
slgs(2),
lcs(3),
bsm(4),
other(9),
...
}
5.21  PacketStats 数据结构
PacketStats ::= SEQUENCE {
packetType   	PacketType,   -- 包类型
packetCount    	Uint32        -- 该类型包的数量
}
5.22  InterfaceType 数据结构
InterfaceType ::= ENUMERATED {
    management,    -- 管理口
    business      -- 业务口
}
5.23  SourceDevice数据结构
SourceDevice ::= SEQUENCE {
    ipAddress IPAddress,   -- 源设备IP地址
    port      INTEGER  OPTIONAL    -- 源设备端口号
}
